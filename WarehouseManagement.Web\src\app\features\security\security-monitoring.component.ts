import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Subject, interval, takeUntil } from 'rxjs';
import { LanguageService } from '../../core/services/language.service';
import { AuthorizationService } from '../../core/services/authorization.service';
import { environment } from '../../../environments/environment';

interface SecurityEvent {
  id: string;
  userId?: string;
  eventType: string;
  description: string;
  ipAddress: string;
  userAgent?: string;
  severity: string;
  occurredAt: Date;
  additionalData?: string;
}

interface LoginAttempt {
  id: string;
  userId?: string;
  username: string;
  ipAddress: string;
  userAgent?: string;
  isSuccessful: boolean;
  failureReason?: string;
  attemptedAt: Date;
}

interface SecurityMetrics {
  totalEvents: number;
  criticalEvents: number;
  failedLogins: number;
  successfulLogins: number;
  uniqueIPs: number;
  lockedAccounts: number;
}

@Component({
  selector: 'app-security-monitoring',
  templateUrl: './security-monitoring.component.html',
  styleUrls: ['./security-monitoring.component.scss']
})
export class SecurityMonitoringComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private readonly API_URL = environment.apiUrl;

  // Data
  securityEvents: SecurityEvent[] = [];
  loginAttempts: LoginAttempt[] = [];
  metrics: SecurityMetrics = {
    totalEvents: 0,
    criticalEvents: 0,
    failedLogins: 0,
    successfulLogins: 0,
    uniqueIPs: 0,
    lockedAccounts: 0
  };

  // UI State
  loading = false;
  selectedTab = 0;
  refreshInterval = 30000; // 30 seconds
  autoRefresh = true;

  // Filters
  eventTypeFilter = '';
  severityFilter = '';
  dateRangeFilter = {
    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
    end: new Date()
  };

  // Chart data
  eventChartData: any;
  loginChartData: any;

  constructor(
    private http: HttpClient,
    public languageService: LanguageService,
    private authorizationService: AuthorizationService
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.setupAutoRefresh();
    this.initializeCharts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupAutoRefresh(): void {
    if (this.autoRefresh) {
      interval(this.refreshInterval)
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          this.loadData();
        });
    }
  }

  loadData(): void {
    this.loading = true;
    
    Promise.all([
      this.loadSecurityEvents(),
      this.loadLoginAttempts(),
      this.loadMetrics()
    ]).finally(() => {
      this.loading = false;
      this.updateCharts();
    });
  }

  private async loadSecurityEvents(): Promise<void> {
    try {
      const params = new URLSearchParams({
        page: '1',
        pageSize: '50',
        ...(this.eventTypeFilter && { eventType: this.eventTypeFilter }),
        ...(this.severityFilter && { severity: this.severityFilter }),
        fromDate: this.dateRangeFilter.start.toISOString(),
        toDate: this.dateRangeFilter.end.toISOString()
      });

      const response = await this.http.get<any>(`${this.API_URL}/security/events?${params}`)
        .toPromise();

      if (response.success) {
        this.securityEvents = response.data || [];
      }
    } catch (error) {
      console.error('Error loading security events:', error);
    }
  }

  private async loadLoginAttempts(): Promise<void> {
    try {
      const params = new URLSearchParams({
        page: '1',
        pageSize: '50',
        fromDate: this.dateRangeFilter.start.toISOString(),
        toDate: this.dateRangeFilter.end.toISOString()
      });

      const response = await this.http.get<any>(`${this.API_URL}/security/login-attempts?${params}`)
        .toPromise();

      if (response.success) {
        this.loginAttempts = response.data || [];
      }
    } catch (error) {
      console.error('Error loading login attempts:', error);
    }
  }

  private async loadMetrics(): Promise<void> {
    // Calculate metrics from loaded data
    this.metrics = {
      totalEvents: this.securityEvents.length,
      criticalEvents: this.securityEvents.filter(e => e.severity === 'Critical').length,
      failedLogins: this.loginAttempts.filter(a => !a.isSuccessful).length,
      successfulLogins: this.loginAttempts.filter(a => a.isSuccessful).length,
      uniqueIPs: new Set(this.loginAttempts.map(a => a.ipAddress)).size,
      lockedAccounts: this.securityEvents.filter(e => e.eventType === 'AccountLocked').length
    };
  }

  private initializeCharts(): void {
    this.eventChartData = {
      labels: ['Critical', 'High', 'Medium', 'Low'],
      datasets: [{
        data: [0, 0, 0, 0],
        backgroundColor: ['#ef4444', '#f97316', '#eab308', '#22c55e']
      }]
    };

    this.loginChartData = {
      labels: ['Successful', 'Failed'],
      datasets: [{
        data: [0, 0],
        backgroundColor: ['#22c55e', '#ef4444']
      }]
    };
  }

  private updateCharts(): void {
    // Update event severity chart
    const severityCounts = {
      Critical: this.securityEvents.filter(e => e.severity === 'Critical').length,
      High: this.securityEvents.filter(e => e.severity === 'High').length,
      Medium: this.securityEvents.filter(e => e.severity === 'Medium').length,
      Low: this.securityEvents.filter(e => e.severity === 'Low').length
    };

    this.eventChartData = {
      ...this.eventChartData,
      datasets: [{
        ...this.eventChartData.datasets[0],
        data: [severityCounts.Critical, severityCounts.High, severityCounts.Medium, severityCounts.Low]
      }]
    };

    // Update login attempts chart
    this.loginChartData = {
      ...this.loginChartData,
      datasets: [{
        ...this.loginChartData.datasets[0],
        data: [this.metrics.successfulLogins, this.metrics.failedLogins]
      }]
    };
  }

  onTabChange(event: any): void {
    this.selectedTab = event.index;
  }

  onRefreshIntervalChange(interval: number): void {
    this.refreshInterval = interval * 1000;
    this.setupAutoRefresh();
  }

  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.setupAutoRefresh();
    }
  }

  applyFilters(): void {
    this.loadData();
  }

  clearFilters(): void {
    this.eventTypeFilter = '';
    this.severityFilter = '';
    this.dateRangeFilter = {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000),
      end: new Date()
    };
    this.loadData();
  }

  exportData(): void {
    const data = {
      securityEvents: this.securityEvents,
      loginAttempts: this.loginAttempts,
      metrics: this.metrics,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  getSeverityClass(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'critical': return 'severity-critical';
      case 'high': return 'severity-high';
      case 'medium': return 'severity-medium';
      case 'low': return 'severity-low';
      default: return '';
    }
  }

  getEventTypeIcon(eventType: string): string {
    switch (eventType.toLowerCase()) {
      case 'loginsuccess': return 'pi-check-circle';
      case 'loginfailure': return 'pi-times-circle';
      case 'logout': return 'pi-sign-out';
      case 'accountlocked': return 'pi-lock';
      case 'suspiciousactivity': return 'pi-exclamation-triangle';
      case 'permissiondenied': return 'pi-ban';
      default: return 'pi-info-circle';
    }
  }

  formatDate(date: Date | string): string {
    const d = new Date(date);
    return d.toLocaleString(this.languageService.isArabic() ? 'ar-SA' : 'en-US');
  }

  // Permission checks
  canViewSecurityEvents(): boolean {
    return this.authorizationService.hasPermission('audit_logs', 'read');
  }

  canExportData(): boolean {
    return this.authorizationService.hasRole('admin');
  }
}
